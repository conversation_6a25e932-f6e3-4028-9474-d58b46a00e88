import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';

export default function SimpleApp() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>🥖 Bakery Tracker Pro</Text>
      <Text style={styles.subtitle}>React Native Version</Text>
      <Text style={styles.description}>
        Complete bakery management system with:
      </Text>
      <Text style={styles.feature}>• POS System with Shopping Cart</Text>
      <Text style={styles.feature}>• Recipe Management</Text>
      <Text style={styles.feature}>• Dashboard Analytics</Text>
      <Text style={styles.feature}>• Role-based Authentication</Text>
      <Text style={styles.feature}>• Indian Bakery Context (₹ pricing)</Text>
      
      <Text style={styles.status}>✅ Conversion Complete!</Text>
      <Text style={styles.instructions}>
        Ready for Expo Go testing
      </Text>
      <StatusBar style="auto" />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
    color: '#6750A4',
  },
  subtitle: {
    fontSize: 18,
    marginBottom: 20,
    color: '#666',
  },
  description: {
    fontSize: 16,
    marginBottom: 15,
    textAlign: 'center',
    fontWeight: '600',
  },
  feature: {
    fontSize: 14,
    marginBottom: 8,
    color: '#333',
  },
  status: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 30,
    marginBottom: 10,
    color: '#4CAF50',
  },
  instructions: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
  },
});
