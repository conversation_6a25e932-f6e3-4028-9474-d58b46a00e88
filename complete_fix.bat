@echo off
echo ========================================
echo Complete Android Build Fix
echo ========================================
echo.

echo Step 1: Stopping Gradle daemons...
call gradlew --stop
echo.

echo Step 2: Clearing corrupted Gradle cache...
echo Removing corrupted commons-text JAR...
rmdir /s /q "%USERPROFILE%\.gradle\caches\modules-2\files-2.1\org.apache.commons\commons-text" 2>nul
echo.

echo Step 3: Clearing all Gradle caches...
rmdir /s /q "%USERPROFILE%\.gradle\caches" 2>nul
rmdir /s /q "%USERPROFILE%\.gradle\daemon" 2>nul
echo.

echo Step 4: Cleaning project...
call gradlew clean --no-daemon
echo.

echo Step 5: Building with fresh dependencies...
echo This will download fresh dependencies and should complete successfully...
call gradlew assembleDebug --no-daemon -x checkDebugAarMetadata --refresh-dependencies
echo.

if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo APK Location: app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo Your Android app has been successfully built!
    echo You can now:
    echo 1. Install the APK on a device using: adb install app\build\outputs\apk\debug\app-debug.apk
    echo 2. Open the project in Android Studio for further development
    echo 3. Use wireless debugging as configured in your setup
    echo.
) else (
    echo ========================================
    echo BUILD FAILED
    echo ========================================
    echo.
    echo If the build still fails, try:
    echo 1. Restart your computer to clear any locked files
    echo 2. Run this script again
    echo 3. Open Android Studio and sync the project
    echo.
)

pause
