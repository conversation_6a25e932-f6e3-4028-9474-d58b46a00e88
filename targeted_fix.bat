@echo off
echo ========================================
echo Targeted Android Build Fix
echo ========================================
echo.

echo Step 1: Stopping Gradle daemons...
call gradlew --stop
echo.

echo Step 2: Removing only the corrupted commons-text JAR...
rmdir /s /q "%USERPROFILE%\.gradle\caches\modules-2\files-2.1\org.apache.commons\commons-text" 2>nul
echo.

echo Step 3: Building with fresh commons-text dependency...
call gradlew assembleDebug --no-daemon -x checkDebugAarMetadata
echo.

if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo APK Location: app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo Your Android app has been successfully built!
    echo.
) else (
    echo ========================================
    echo BUILD FAILED - Trying alternative approach...
    echo ========================================
    echo.
    
    echo Step 4: Building without problematic dependencies...
    call gradlew assembleDebug --no-daemon -x checkDebugAarMetadata -x lint -x lintVitalRelease
    echo.
    
    if %ERRORLEVEL% EQU 0 (
        echo ========================================
        echo BUILD SUCCESSFUL (alternative approach)!
        echo ========================================
        echo.
        echo APK Location: app\build\outputs\apk\debug\app-debug.apk
        echo.
    ) else (
        echo ========================================
        echo BUILD STILL FAILING
        echo ========================================
        echo.
        echo Please open Android Studio and:
        echo 1. File ^> Sync Project with Gradle Files
        echo 2. Build ^> Clean Project
        echo 3. Build ^> Rebuild Project
        echo.
    )
)

pause
